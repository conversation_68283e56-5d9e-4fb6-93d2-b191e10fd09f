using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Win32;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// Raylib-based main window to replace WPF MainWindow
    /// </summary>
    public class RaylibMainWindow : IDisposable
    {
        private RaylibScene scene;
        private Settings settings;
        private bool isRunning = false;

        // Window properties
        private int windowWidth = 1200;
        private int windowHeight = 700;
        private string windowTitle = "Kiva MIDI Visualizer";
        private bool isFullscreen = false;
        private int windowedWidth = 1200;  // Store windowed size
        private int windowedHeight = 700;

        // MIDI playback
        private MIDIPlayer midiPlayer;
        private MIDIPreRenderPlayer preRenderPlayer;
        private PlayingState playingState;
        private AudioEngine selectedAudioEngine;

        // UI state
        private bool showUI = true;
        private bool showInfoPanel = true;
        private string statusMessage = "";
        private DateTime statusMessageTime = DateTime.MinValue;

        // MainWindow UI colors (matching original XAML)
        private static readonly RaylibColor TITLE_BAR_COLOR = new RaylibColor(0, 104, 201, 255); // #0068c9
        private static readonly RaylibColor BUTTON_HOVER_COLOR = new RaylibColor(255, 255, 255, 100);
        private static readonly RaylibColor INFO_PANEL_BG = new RaylibColor(0, 0, 0, 128);
        private static readonly RaylibColor SLIDER_TRACK_COLOR = new RaylibColor(100, 100, 100, 255); // Lighter track
        private static readonly RaylibColor SLIDER_FILL_COLOR = new RaylibColor(0, 0, 0, 255); // Black stretching texture
        private static readonly RaylibColor SELECTOR_COLOR = new RaylibColor(0, 0, 0, 255); // Black circle selector

        // UI layout constants
        private const int TITLE_BAR_HEIGHT = 69;
        private const int BUTTON_SIZE = 40;
        private const int SLIDER_WIDTH = 220;
        private const int INFO_PANEL_WIDTH = 250;

        // Font constants (matching MainWindow.xaml)
        private const int DEFAULT_FONT_SIZE = 13;  // TextElement.FontSize="13"
        private const int LABEL_FONT_SIZE = 16;    // FontSize="16" for labels
        private const int TITLE_FONT_SIZE = 20;    // For titles
        private const int SMALL_FONT_SIZE = 12;    // For small text

        // Note speed control
        private double noteSpeed = 1.0; // Default speed multiplier
        private const double MIN_NOTE_SPEED = 0.01;
        private const double MAX_NOTE_SPEED = 5.0;
        private const double SPEED_STEP = 0.1;

        // Seeking control
        private const double SEEK_STEP = 5.0; // 5 seconds per arrow key press
        private bool isDraggingSeekBar = false;

        // Slider dragging
        private bool isDraggingSpeedSlider = false;
        private bool isDraggingSizeSlider = false;

        // Store exact mouse positions for accurate circle rendering
        private int speedSliderMouseX = -1;
        private int sizeSliderMouseX = -1;

        // Store pending values to apply when dragging stops
        private double pendingSpeedValue = -1;
        private double pendingSizeValue = -1;

        // Settings window management
        private static SettingsWindow currentSettingsWindow = null;
        private static readonly object settingsWindowLock = new object();

        // Text input for number editing
        private bool isEditingSpeedNumber = false;
        private bool isEditingSizeNumber = false;
        private string speedInputText = "";
        private string sizeInputText = "";

        // Font handling (TTF loading disabled)
        // Using default Raylib font for stability

        // Performance tracking
        private DateTime lastFrameTime = DateTime.UtcNow;
        private double currentFPS = 60.0;
        private int frameCount = 0;
        private DateTime fpsUpdateTime = DateTime.UtcNow;

        // Rendering statistics
        private int lastRenderedNotes = 0;
        private int currentPolyphony = 0;
        private DateTime lastStatsUpdate = DateTime.UtcNow;

        // Font scaling for better text rendering
        private const float FONT_SCALE = 1.2f; // Scale factor to make text more readable

        public Settings Settings => settings;
        public MIDIFile CurrentFile => scene?.File;

        public RaylibMainWindow()
        {
            settings = new Settings(); // Settings are loaded automatically in constructor
            settings.InitSoundfontListner(); // Initialize soundfont listener

            // Force advanced settings as requested
            settings.General.DisableTransparency = true;
            settings.General.MultiThreadedRendering = false;

            playingState = new PlayingState();

            // Initialize audio system
            MIDIAudio.Init();

            // Start the appropriate audio engine based on settings
            selectedAudioEngine = settings.General.SelectedAudioEngine;
            InitializeAudioEngine();

            // Initialize note speed from settings
            if (settings.Volatile != null)
            {
                noteSpeed = settings.Volatile.Size;
            }

            // Subscribe to settings changes
            settings.General.PropertyChanged += OnSettingsChanged;
        }

        public void Initialize()
        {
            scene = new RaylibScene(settings);
            scene.Initialize(windowWidth, windowHeight, windowTitle);
            scene.Time = playingState;

            // Apply initial settings
            UpdateFPSSettings();
            ApplyKeyboardSettings();

            // Generate note.png with white note in the same style as the current note rendering
            // This will also automatically try to load the texture after generation
            scene.Renderer.GenerateNotePNG();

            // Initialize note texture system (loads existing note.png if generation didn't work)
            scene.Renderer.InitializeNoteTexture();
        }

        private void DrawTextWithFont(string text, int x, int y, int fontSize, RaylibColor color)
        {
            // Always use default Raylib font (TTF loading disabled)
            Raylib.DrawText(text, x, y, fontSize, color);
        }

        private void OnSettingsChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            // Handle settings changes in real-time
            switch (e.PropertyName)
            {
                case nameof(GeneralSettings.FPSLock):
                case nameof(GeneralSettings.SyncFPS):
                    UpdateFPSSettings();
                    break;

                case nameof(GeneralSettings.KeyRange):
                case nameof(GeneralSettings.CustomFirstKey):
                case nameof(GeneralSettings.CustomLastKey):
                case nameof(GeneralSettings.KeyboardStyle):
                    ApplyKeyboardSettings();
                    break;

                case nameof(GeneralSettings.PaletteName):
                case nameof(GeneralSettings.PaletteRandomized):
                    ApplyPaletteSettings();
                    break;

                case nameof(GeneralSettings.SelectedAudioEngine):
                    SwitchAudioEngine(settings.General.SelectedAudioEngine);
                    break;

                case nameof(GeneralSettings.HideInfoCard):
                    showInfoPanel = !settings.General.HideInfoCard;
                    break;

                case nameof(GeneralSettings.InfoCardParams):
                    // Info card parameters changed - no action needed, will be applied on next render
                    SetStatusMessage("Info card display updated");
                    break;

                case nameof(GeneralSettings.BackgroundColor):
                case nameof(GeneralSettings.ForegroundColor):
                case nameof(GeneralSettings.BarColor):
                    // Update renderer colors
                    scene.Renderer.UpdateColors();
                    SetStatusMessage("Colors updated");
                    break;

                case nameof(GeneralSettings.MainWindowTopmost):
                    // Note: Raylib doesn't have built-in topmost, but we can track it
                    SetStatusMessage($"Topmost: {settings.General.MainWindowTopmost}");
                    break;
            }
        }

        private void UpdateFPSSettings()
        {
            if (settings.General.SyncFPS)
            {
                // Use VSync (monitor refresh rate)
                Raylib.SetTargetFPS(0); // 0 means VSync in Raylib
                SetStatusMessage("FPS: VSync enabled");
            }
            else
            {
                // Use custom FPS lock
                int targetFPS = settings.General.FPSLock;
                if (targetFPS == 0)
                {
                    // 0 means unlimited FPS when sync is disabled
                    Raylib.SetTargetFPS(0);
                    SetStatusMessage("FPS: Unlimited");
                }
                else
                {
                    // Clamp to reasonable range
                    targetFPS = Math.Max(1, Math.Min(300, targetFPS));
                    Raylib.SetTargetFPS(targetFPS);
                    SetStatusMessage($"FPS locked to: {targetFPS}");
                }
            }
        }

        private void ApplyKeyboardSettings()
        {
            if (scene?.Renderer == null) return;

            // Apply keyboard style
            // The renderer will check settings.General.KeyboardStyle when rendering

            // Apply key range
            int firstKey = 0;
            int lastKey = 127;

            // Cast to MIDIMemoryFile once for reuse
            MIDIMemoryFile memoryFile = scene.File as MIDIMemoryFile;

            switch (settings.General.KeyRange)
            {
                case KeyRangeTypes.Key88:
                    firstKey = 21; // A0
                    lastKey = 108; // C8
                    break;
                case KeyRangeTypes.Key128:
                    firstKey = 0;
                    lastKey = 127;
                    break;
                case KeyRangeTypes.Key256:
                    firstKey = 0;
                    lastKey = 255;
                    break;
                case KeyRangeTypes.KeyMIDI:
                    // Use the actual range from the loaded MIDI file
                    if (memoryFile != null)
                    {
                        // Find actual key range in MIDI file
                        firstKey = 127;
                        lastKey = 0;
                        for (int i = 0; i < memoryFile.Notes.Length; i++)
                        {
                            if (memoryFile.Notes[i] != null && memoryFile.Notes[i].Length > 0)
                            {
                                firstKey = Math.Min(firstKey, i);
                                lastKey = Math.Max(lastKey, i);
                            }
                        }
                        if (firstKey > lastKey) { firstKey = 0; lastKey = 127; }
                    }
                    break;
                case KeyRangeTypes.KeyDynamic:
                    // Dynamic range (88-128 based on content)
                    if (memoryFile != null)
                    {
                        // Find range and expand to 88 or 128 keys
                        int midiFirst = 127, midiLast = 0;
                        for (int i = 0; i < memoryFile.Notes.Length; i++)
                        {
                            if (memoryFile.Notes[i] != null && memoryFile.Notes[i].Length > 0)
                            {
                                midiFirst = Math.Min(midiFirst, i);
                                midiLast = Math.Max(midiLast, i);
                            }
                        }

                        // Expand to 88 keys if range is small, 128 if larger
                        int range = midiLast - midiFirst;
                        if (range <= 88)
                        {
                            firstKey = 21; lastKey = 108; // 88 keys
                        }
                        else
                        {
                            firstKey = 0; lastKey = 127; // 128 keys
                        }
                    }
                    else
                    {
                        firstKey = 21; lastKey = 108; // Default to 88 keys
                    }
                    break;
                case KeyRangeTypes.Custom:
                    firstKey = Math.Max(0, Math.Min(255, settings.General.CustomFirstKey));
                    lastKey = Math.Max(firstKey, Math.Min(255, settings.General.CustomLastKey));
                    break;
            }

            // Store the calculated key range in settings for renderer to use
            settings.General.FirstKey = firstKey;
            settings.General.LastKey = lastKey;

            // Update the renderer's keyboard layout
            scene.Renderer.UpdateKeyboardSettings();

            SetStatusMessage($"Key range: {firstKey}-{lastKey} ({settings.General.KeyRange})");
        }

        private void ApplyPaletteSettings()
        {
            if (scene?.File == null) return;

            try
            {
                // Reload palette and apply to current file
                var palette = settings.PaletteSettings.Palettes[settings.General.PaletteName];
                scene.File.SetColors(palette, settings.General.PaletteRandomized);
                SetStatusMessage($"Palette: {settings.General.PaletteName} (Randomized: {settings.General.PaletteRandomized})");
            }
            catch (Exception ex)
            {
                SetStatusMessage($"Error applying palette: {ex.Message}");
            }
        }

        public void Run()
        {
            if (isRunning)
                return;

            Initialize();
            isRunning = true;

            // Run the main loop on the main thread (raylib requirement)
            MainLoop();
        }

        private void MainLoop()
        {
            Console.WriteLine("Kiva MIDI Visualizer - Raylib Version");
            Console.WriteLine("Recreating the original MainWindow.xaml design with raylib!");
            Console.WriteLine("Features:");
            Console.WriteLine("- Blue title bar with control buttons and sliders");
            Console.WriteLine("- Info panel on the left (press H to toggle)");
            Console.WriteLine("- Time slider in title bar for seeking");
            Console.WriteLine("- Left/Right arrows to seek, Shift+Up/Down for note speed");
            Console.WriteLine("- ENTER to toggle fullscreen, ESC to exit fullscreen/app");
            Console.WriteLine("- Resizable window with real-time scaling");
            Console.WriteLine($"Current audio engine: {selectedAudioEngine}");
            Console.WriteLine();

            try
            {
                while (isRunning && !scene.ShouldClose())
                {
                    try
                    {
                        // Update FPS calculation
                        UpdateFPS();

                        // Handle raylib input
                        HandleRaylibInput();

                        // Handle window resize
                        HandleWindowResize();

                        // Begin the drawing cycle
                        Raylib.BeginDrawing();

                        // Clear background
                        Raylib.ClearBackground(RaylibColor.BLACK);

                        // Render MIDI content first (background layer)
                        scene.RenderMIDIContent();

                        // Update rendering statistics
                        UpdateRenderingStats();

                        // Render MainWindow UI on top (foreground layer)
                        RenderMainWindowUI();

                        // End the drawing cycle
                        Raylib.EndDrawing();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Frame error: {ex.Message}");
                        // Continue despite errors
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Main loop error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            finally
            {
                isRunning = false;
                Console.WriteLine("Shutting down...");
            }
        }

        private void HandleRaylibInput()
        {
            // Handle keyboard input through raylib
            if (Raylib.IsKeyPressed(RaylibPInvoke.KEY_ESCAPE))
            {
                // If fullscreen, exit fullscreen first, otherwise exit app
                if (isFullscreen)
                {
                    ToggleFullscreen();
                }
                else
                {
                    Quit();
                }
            }

            // Fullscreen toggle
            if (Raylib.IsKeyPressed(RaylibPInvoke.KEY_ENTER))
            {
                ToggleFullscreen();
            }

            // Handle text input for number editing
            if (isEditingSpeedNumber || isEditingSizeNumber)
            {
                HandleTextInput();
            }
            else
            {
                // File operations
                if (Raylib.IsKeyPressed(KEY_O))
                {
                    OpenMIDIFileDialog();
                }

                // Playback controls
                if (Raylib.IsKeyPressed(KEY_SPACE) || Raylib.IsKeyPressed(KEY_P))
                {
                    TogglePlayback();
                }
            }

            if (Raylib.IsKeyPressed(KEY_S))
            {
                StopPlayback();
            }

            // Audio engine switching removed (no longer needed)

            // UI toggle
            if (Raylib.IsKeyPressed(KEY_H))
            {
                showInfoPanel = !showInfoPanel;
                SetStatusMessage(showInfoPanel ? "Info panel shown" : "Info panel hidden");
            }

            // Info display
            if (Raylib.IsKeyPressed(KEY_I))
            {
                ShowInfoOnScreen();
            }

            // Toggle segment rendering
            if (Raylib.IsKeyPressed(KEY_F4))
            {
                ToggleSegmentRendering();
            }

            // Mouse interaction for seek bar
            HandleSeekBarMouse();

            // Mouse interaction for sliders
            HandleSliderMouse();

            // Mouse interaction for buttons
            HandleButtonMouse();

            // Handle clicking off text input boxes
            HandleTextInputClickOff();

            // Arrow key controls
            bool shiftPressed = Raylib.IsKeyDown(KEY_LEFT_SHIFT) || Raylib.IsKeyDown(KEY_RIGHT_SHIFT);

            if (shiftPressed)
            {
                // Note speed controls (Shift + Up/Down)
                if (Raylib.IsKeyPressed(KEY_UP))
                {
                    ChangeNoteSpeed(-SPEED_STEP); // Slower (up arrow = slower)
                }
                else if (Raylib.IsKeyPressed(KEY_DOWN))
                {
                    ChangeNoteSpeed(SPEED_STEP); // Faster (down arrow = faster)
                }
            }
            else
            {
                // Seeking controls (Left/Right arrows without Shift)
                if (Raylib.IsKeyPressed(KEY_LEFT))
                {
                    SeekBackward();
                }
                else if (Raylib.IsKeyPressed(KEY_RIGHT))
                {
                    SeekForward();
                }
            }
        }

        private void HandleConsoleKey(char key)
        {
            switch (char.ToLower(key))
            {
                case 'o':
                    OpenMIDIFileDialog();
                    break;
                case 'p':
                    TogglePlayback();
                    break;
                case 's':
                    StopPlayback();
                    break;
                case 'q':
                    Quit();
                    break;
                case '1':
                    SwitchAudioEngine(AudioEngine.KDMAPI);
                    break;
                case '2':
                    SwitchAudioEngine(AudioEngine.WinMM);
                    break;
                case '3':
                    SwitchAudioEngine(AudioEngine.PreRender);
                    break;
                case 'i':
                    ShowInfo();
                    break;
                default:
                    Console.WriteLine($"Unknown command: {key}");
                    break;
            }
        }

        private void OpenMIDIFileDialog()
        {
            SetStatusMessage("Opening file dialog...");

            // Use proper Windows file dialog (same as the main WPF window)
            Task.Run(() =>
            {
                try
                {
                    // Create and configure the file dialog on the UI thread
                    string selectedFile = null;
                    var dialogTask = Task.Run(() =>
                    {
                        // Use STA thread for file dialog
                        var thread = new Thread(() =>
                        {
                            var openFileDialog = new Microsoft.Win32.OpenFileDialog();
                            openFileDialog.Filter = "MIDI files (*.mid)|*.mid|All files (*.*)|*.*";
                            openFileDialog.Title = "Select MIDI File";
                            openFileDialog.CheckFileExists = true;
                            openFileDialog.CheckPathExists = true;

                            // Show the dialog
                            bool? result = openFileDialog.ShowDialog();
                            if (result == true)
                            {
                                selectedFile = openFileDialog.FileName;
                            }
                        });

                        thread.SetApartmentState(ApartmentState.STA);
                        thread.Start();
                        thread.Join();
                    });

                    dialogTask.Wait();

                    if (string.IsNullOrEmpty(selectedFile))
                    {
                        SetStatusMessage("File selection cancelled");
                        return;
                    }

                    if (!System.IO.File.Exists(selectedFile))
                    {
                        SetStatusMessage("Selected file does not exist");
                        return;
                    }

                    LoadMIDIFile(selectedFile);
                    SetStatusMessage($"Loaded: {System.IO.Path.GetFileName(selectedFile)}");
                }
                catch (Exception ex)
                {
                    SetStatusMessage($"Error opening file dialog: {ex.Message}");
                    Console.WriteLine($"Error opening file dialog: {ex.Message}");
                }
            });
        }

        private void LoadMIDIFile(string filePath)
        {
            // Stop current playback
            StopPlayback();

            // PROPERLY CLOSE any existing MIDI file to prevent resource leaks
            if (scene?.File != null)
            {
                try
                {
                    // Clear the file reference to release resources
                    scene.File = null;
                    GC.Collect(); // Force garbage collection to free memory
                    SetStatusMessage("Closed previous MIDI file");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error closing previous MIDI file: {ex.Message}");
                }
            }

            SetStatusMessage("Opening MIDI loading window...");

            // Use the proper loading window with progress and settings
            Task.Run(() =>
            {
                try
                {
                    var thread = new Thread(() =>
                    {
                        try
                        {
                            var loadingWindow = new LoadingMidiForm(filePath, settings);

                            // Handle successful parsing
                            loadingWindow.ParseFinished += () =>
                            {
                                try
                                {
                                    var midiFile = loadingWindow.LoadedFile;
                                    if (midiFile != null)
                                    {
                                        // Set up colors for the MIDI file
                                        midiFile.SetColors(settings.PaletteSettings.Palettes[settings.General.PaletteName], settings.General.PaletteRandomized);

                                        // Set up the file in the scene
                                        scene.File = midiFile;

                                        // Set up the file in the audio player
                                        if (selectedAudioEngine == AudioEngine.PreRender)
                                        {
                                            preRenderPlayer.File = midiFile;
                                        }
                                        else
                                        {
                                            midiPlayer.File = midiFile;
                                        }

                                        playingState.Reset();
                                        SetStatusMessage($"Loaded: {System.IO.Path.GetFileName(filePath)}");
                                        Console.WriteLine("MIDI file loaded successfully!");
                                    }
                                    else
                                    {
                                        SetStatusMessage("Failed to load MIDI file");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    SetStatusMessage($"Error setting up MIDI file: {ex.Message}");
                                    Console.WriteLine($"Error setting up MIDI file: {ex.Message}");
                                }
                                finally
                                {
                                    // Close the loading window
                                    loadingWindow.Dispatcher.InvokeAsync(() =>
                                    {
                                        loadingWindow.Close();
                                        loadingWindow.Dispose();
                                    });
                                }
                            };

                            // Handle cancelled parsing
                            loadingWindow.ParseCancelled += () =>
                            {
                                SetStatusMessage("MIDI loading cancelled");
                                loadingWindow.Dispatcher.InvokeAsync(() =>
                                {
                                    loadingWindow.Close();
                                    loadingWindow.Dispose();
                                });
                            };

                            // Show the loading window
                            loadingWindow.ShowDialog();
                        }
                        catch (Exception ex)
                        {
                            SetStatusMessage($"Error opening loading window: {ex.Message}");
                            Console.WriteLine($"Error opening loading window: {ex.Message}");
                        }
                    });

                    thread.SetApartmentState(ApartmentState.STA);
                    thread.Start();
                    thread.Join();
                }
                catch (Exception ex)
                {
                    SetStatusMessage($"Error loading MIDI file: {ex.Message}");
                    Console.WriteLine($"Error loading MIDI file: {ex.Message}");
                }
            });
        }

        private void TogglePlayback()
        {
            if (scene.File == null)
            {
                SetStatusMessage("No MIDI file loaded");
                return;
            }

            if (!playingState.Paused)
            {
                PausePlayback();
            }
            else
            {
                StartPlayback();
            }
        }

        private void StartPlayback()
        {
            if (scene.File == null)
                return;

            try
            {
                playingState.Play();
                SetStatusMessage("Playback started");
            }
            catch (Exception ex)
            {
                SetStatusMessage($"Error starting playback: {ex.Message}");
            }
        }

        private void PausePlayback()
        {
            try
            {
                playingState.Pause();
                SetStatusMessage("Playback paused");
            }
            catch (Exception ex)
            {
                SetStatusMessage($"Error pausing playback: {ex.Message}");
            }
        }

        private void StopPlayback()
        {
            try
            {
                playingState.Reset();
                SetStatusMessage("Playback stopped");
            }
            catch (Exception ex)
            {
                SetStatusMessage($"Error stopping playback: {ex.Message}");
            }
        }

        private void Quit()
        {
            Console.WriteLine("Shutting down...");
            isRunning = false;
        }

        private void InitializeAudioEngine()
        {
            switch (selectedAudioEngine)
            {
                case AudioEngine.KDMAPI:
                    StartMIDIPlayer(true);
                    break;
                case AudioEngine.WinMM:
                    StartMIDIPlayer(false);
                    break;
                case AudioEngine.PreRender:
                    StartPreRenderPlayer();
                    break;
            }
        }

        private void StartMIDIPlayer(bool useKDMAPI)
        {
            preRenderPlayer?.Dispose();
            preRenderPlayer = null;

            midiPlayer = new MIDIPlayer(settings);
            midiPlayer.Time = playingState;
            midiPlayer.DeviceID = useKDMAPI ? -1 : settings.General.SelectedMIDIDevice;
            midiPlayer.RunPlayer();

            Console.WriteLine($"Started MIDI player with {(useKDMAPI ? "KDMAPI" : "WinMM")}");
        }

        private void StartPreRenderPlayer()
        {
            midiPlayer?.Dispose();
            midiPlayer = null;

            preRenderPlayer = new MIDIPreRenderPlayer(settings);
            preRenderPlayer.Time = playingState;

            Console.WriteLine("Started pre-render audio player");
        }

        private void SwitchAudioEngine(AudioEngine engine)
        {
            if (engine == selectedAudioEngine)
            {
                SetStatusMessage($"Already using {engine} audio");
                return;
            }

            SetStatusMessage($"Switching to {engine} audio...");

            var currentFile = scene?.File;
            var currentTime = playingState.GetTime();
            var wasPaused = playingState.Paused;

            // Dispose current audio engine
            midiPlayer?.Dispose();
            preRenderPlayer?.Dispose();
            midiPlayer = null;
            preRenderPlayer = null;

            // Switch to new engine
            selectedAudioEngine = engine;
            settings.General.SelectedAudioEngine = engine;
            InitializeAudioEngine();

            // Restore file and state
            if (currentFile != null)
            {
                if (selectedAudioEngine == AudioEngine.PreRender)
                {
                    preRenderPlayer.File = currentFile;
                }
                else
                {
                    midiPlayer.File = currentFile;
                }

                // Restore playback position and state
                playingState.Navigate(currentTime);
                if (!wasPaused)
                {
                    playingState.Play();
                }
                else
                {
                    playingState.Pause();
                }
            }

            SetStatusMessage($"Switched to {engine} audio");
        }

        private void ShowInfo()
        {
            Console.WriteLine("=== Kiva MIDI Status ===");
            Console.WriteLine($"Audio Engine: {selectedAudioEngine}");

            if (scene?.File != null)
            {
                Console.WriteLine($"File: {System.IO.Path.GetFileName(scene.File.filepath)}");
                Console.WriteLine($"Length: {scene.File.MidiLength:F2}s");
                Console.WriteLine($"Current Time: {playingState.GetTime():F2}s");
                Console.WriteLine($"Status: {(playingState.Paused ? "Paused" : "Playing")}");
                Console.WriteLine($"Speed: {playingState.Speed:F2}x");
                Console.WriteLine($"Note Count: {scene.File.MidiNoteCount:N0}");
                Console.WriteLine($"Key Range: {scene.File.FirstKey} - {scene.File.LastKey}");

                if (selectedAudioEngine == AudioEngine.PreRender && preRenderPlayer != null)
                {
                    Console.WriteLine($"Buffer: {preRenderPlayer.BufferSeconds:F2}s");
                }
            }
            else
            {
                Console.WriteLine("No MIDI file loaded");
            }
            Console.WriteLine("========================");
        }

        private void ShowInfoOnScreen()
        {
            if (scene?.File != null)
            {
                string info = $"File: {System.IO.Path.GetFileName(scene.File.filepath)} | " +
                             $"Length: {scene.File.MidiLength:F1}s | " +
                             $"Notes: {scene.File.MidiNoteCount:N0} | " +
                             $"Keys: {scene.File.FirstKey}-{scene.File.LastKey} | " +
                             $"Speed: {noteSpeed:F1}x";
                SetStatusMessage(info);
            }
            else
            {
                SetStatusMessage($"No MIDI file loaded | Speed: {noteSpeed:F1}x");
            }
        }

        private void RenderMainWindowUI()
        {
            // Render title bar (matching original #0068c9 blue)
            RenderTitleBar();

            // Render info panel (left side, matching original)
            if (showInfoPanel)
            {
                RenderInfoPanel();
            }

            // Render status message overlay
            RenderStatusMessage();
        }

        private void RenderTitleBar()
        {
            // Title bar background
            Raylib.DrawRectangle(0, 0, windowWidth, TITLE_BAR_HEIGHT, TITLE_BAR_COLOR);

            // Control buttons row (top row)
            int buttonY = 5;
            int buttonX = 10;

            // Open button
            RenderButton(buttonX, buttonY, BUTTON_SIZE, BUTTON_SIZE, "O", "Open file");
            buttonX += BUTTON_SIZE + 5;

            // Pause button
            RenderButton(buttonX, buttonY, BUTTON_SIZE, BUTTON_SIZE, "||", "Pause");
            buttonX += BUTTON_SIZE + 5;

            // Play button
            RenderButton(buttonX, buttonY, BUTTON_SIZE, BUTTON_SIZE, "▶", "Play");
            buttonX += BUTTON_SIZE + 5;

            // Settings button
            RenderButton(buttonX, buttonY, BUTTON_SIZE, BUTTON_SIZE, "⚙", "Settings");
            buttonX += BUTTON_SIZE + 15;

            // Speed slider with clickable number and arrows
            DrawTextWithFont("Speed:", buttonX, buttonY + 5, (int)(LABEL_FONT_SIZE * FONT_SCALE), RaylibColor.WHITE);
            buttonX += 70;
            RenderSliderWithControls(buttonX, buttonY + 10, SLIDER_WIDTH, 20, playingState.Speed, 0.1, 10.0, "Speed", true);
            buttonX += SLIDER_WIDTH + 100; // More space to avoid overlap

            // Size slider with clickable number and arrows (moved further right)
            DrawTextWithFont("Size:", buttonX, buttonY + 5, (int)(LABEL_FONT_SIZE * FONT_SCALE), RaylibColor.WHITE);
            buttonX += 60;
            RenderSliderWithControls(buttonX, buttonY + 10, SLIDER_WIDTH, 20, noteSpeed, MIN_NOTE_SPEED, MAX_NOTE_SPEED, "Size", false);

            // Window controls (right side)
            int closeX = windowWidth - 30;
            int minimizeX = closeX - 35;

            // Minimize button (orange)
            Raylib.DrawRectangle(minimizeX, buttonY, 25, 20, new RaylibColor(255, 165, 0, 255));
            Raylib.DrawText("_", minimizeX + 8, buttonY + 2, (int)(LABEL_FONT_SIZE * FONT_SCALE), RaylibColor.BLACK);

            // Close button (red)
            Raylib.DrawRectangle(closeX, buttonY, 25, 20, new RaylibColor(255, 0, 0, 255));
            Raylib.DrawText("X", closeX + 8, buttonY + 2, (int)(LABEL_FONT_SIZE * FONT_SCALE), RaylibColor.WHITE);

            // Time slider (bottom row of title bar)
            int timeSliderY = TITLE_BAR_HEIGHT - 25;
            int timeSliderMargin = 10;
            int timeSliderWidth = windowWidth - (timeSliderMargin * 2);

            if (scene?.File != null)
            {
                double currentTime = playingState.GetTime();
                double totalTime = scene.File.MidiLength;
                double progress = totalTime > 0 ? currentTime / totalTime : 0;

                // Time slider - thin track (3px height)
                int trackHeight = 3;
                int trackY = timeSliderY + 6; // Center the thin track
                Raylib.DrawRectangle(timeSliderMargin, trackY, timeSliderWidth, trackHeight, SLIDER_TRACK_COLOR);

                // Time slider progress - thin black stretching texture
                int progressWidth = (int)(timeSliderWidth * progress);
                if (progressWidth > 0)
                {
                    Raylib.DrawRectangle(timeSliderMargin, trackY, progressWidth, trackHeight, SLIDER_FILL_COLOR);
                }

                // Time slider selector - small black circle (properly aligned)
                int selectorX = timeSliderMargin + progressWidth; // Position based on progress
                int selectorY = timeSliderY + 7; // Center the selector vertically
                int selectorRadius = 6; // Smaller radius

                // Try to draw circle, fallback to rectangle
                try
                {
                    Raylib.DrawCircle(selectorX, selectorY, selectorRadius, SELECTOR_COLOR);
                }
                catch
                {
                    // Fallback: Draw a small square centered on the position
                    Raylib.DrawRectangle(selectorX - selectorRadius, selectorY - selectorRadius,
                                       selectorRadius * 2, selectorRadius * 2, SELECTOR_COLOR);
                }
            }
        }

        private void RenderButton(int x, int y, int width, int height, string text, string tooltip)
        {
            // Button background
            Raylib.DrawRectangle(x, y, width, height, new RaylibColor(255, 255, 255, 50));
            Raylib.DrawRectangleLines(x, y, width, height, RaylibColor.WHITE);

            // Button text (centered)
            int fontSize = (int)(LABEL_FONT_SIZE * FONT_SCALE);
            int textWidth = MeasureTextWidth(text, fontSize);
            int textX = x + (width - textWidth) / 2;
            int textY = y + (height - fontSize) / 2;
            Raylib.DrawText(text, textX, textY, fontSize, RaylibColor.WHITE);
        }

        private void RenderSlider(int x, int y, int width, int height, double value, double min, double max, string name)
        {
            // Thin slider track (3px height)
            int trackHeight = 3;
            int trackY = y + (height - trackHeight) / 2; // Center the thin track
            Raylib.DrawRectangle(x, trackY, width, trackHeight, SLIDER_TRACK_COLOR);

            // Slider selector - small black circle
            int selectorX;

            // ALWAYS use exact mouse position when dragging (don't let value override it)
            if (name == "Speed" && isDraggingSpeedSlider && speedSliderMouseX >= 0)
            {
                selectorX = Math.Max(x, Math.Min(x + width, speedSliderMouseX)); // Stay exactly where user put it
            }
            else if (name == "Size" && isDraggingSizeSlider && sizeSliderMouseX >= 0)
            {
                selectorX = Math.Max(x, Math.Min(x + width, sizeSliderMouseX)); // Stay exactly where user put it
            }
            else
            {
                // Only use calculated position when NOT dragging
                double progress = (value - min) / (max - min);
                progress = Math.Max(0, Math.Min(1, progress));
                selectorX = x + (int)(width * progress);
            }

            // Slider fill - thin black stretching texture (fill to circle position)
            int fillWidth = selectorX - x; // Fill to where the circle is
            if (fillWidth > 0)
            {
                Raylib.DrawRectangle(x, trackY, fillWidth, trackHeight, SLIDER_FILL_COLOR);
            }

            int selectorY = y + height / 2; // Center the selector vertically
            int selectorRadius = 6; // Smaller radius

            // Try to draw circle, fallback to rectangle
            try
            {
                Raylib.DrawCircle(selectorX, selectorY, selectorRadius, SELECTOR_COLOR);
            }
            catch
            {
                // Fallback: Draw a small square centered on the position
                Raylib.DrawRectangle(selectorX - selectorRadius, selectorY - selectorRadius,
                                   selectorRadius * 2, selectorRadius * 2, SELECTOR_COLOR);
            }

            // Value text
            string valueText = $"{value:F2}";
            Raylib.DrawText(valueText, x + width + 10, y + 2, (int)(DEFAULT_FONT_SIZE * FONT_SCALE), RaylibColor.WHITE);
        }

        private void RenderSliderWithControls(int x, int y, int width, int height, double value, double min, double max, string name, bool isSpeed)
        {
            // Render the slider part
            RenderSlider(x, y, width, height, value, min, max, name);

            // Render clickable number display with background
            int numberX = x + width + 10;
            int numberY = y;
            int numberWidth = 60;
            int numberHeight = height;

            // Background for number display
            RaylibColor numberBg = new RaylibColor(60, 60, 60, 255);
            Raylib.DrawRectangle(numberX, numberY, numberWidth, numberHeight, numberBg);
            Raylib.DrawRectangleLines(numberX, numberY, numberWidth, numberHeight, RaylibColor.WHITE);

            // Value text (centered) - show input text if editing
            string valueText;
            RaylibColor textColor = RaylibColor.WHITE;

            if ((isSpeed && isEditingSpeedNumber) || (!isSpeed && isEditingSizeNumber))
            {
                valueText = isSpeed ? speedInputText : sizeInputText;
                textColor = new RaylibColor(255, 255, 0, 255); // Yellow when editing

                // Add cursor indicator (simple blinking using frame count)
                if ((DateTime.Now.Millisecond / 500) % 2 == 0) // Blinking cursor every 500ms
                {
                    valueText += "|";
                }
            }
            else
            {
                valueText = $"{value:F2}";
            }

            int textWidth = MeasureTextWidth(valueText, (int)(DEFAULT_FONT_SIZE * FONT_SCALE));
            int textX = numberX + (numberWidth - textWidth) / 2;
            int textY = numberY + 2;
            DrawTextWithFont(valueText, textX, textY, (int)(DEFAULT_FONT_SIZE * FONT_SCALE), textColor);

            // Up/Down arrows
            int arrowX = numberX + numberWidth + 5;
            int arrowWidth = 15;
            int arrowHeight = height / 2 - 1;

            // Up arrow
            RaylibColor upColor = new RaylibColor(80, 80, 80, 255);
            Raylib.DrawRectangle(arrowX, numberY, arrowWidth, arrowHeight, upColor);
            Raylib.DrawRectangleLines(arrowX, numberY, arrowWidth, arrowHeight, RaylibColor.WHITE);
            Raylib.DrawText("▲", arrowX + 3, numberY - 2, 12, RaylibColor.WHITE);

            // Down arrow
            RaylibColor downColor = new RaylibColor(80, 80, 80, 255);
            Raylib.DrawRectangle(arrowX, numberY + arrowHeight + 2, arrowWidth, arrowHeight, downColor);
            Raylib.DrawRectangleLines(arrowX, numberY + arrowHeight + 2, arrowWidth, arrowHeight, RaylibColor.WHITE);
            Raylib.DrawText("▼", arrowX + 3, numberY + arrowHeight, 12, RaylibColor.WHITE);
        }

        private void RenderInfoPanel()
        {
            int margin = 10;
            int currentY = TITLE_BAR_HEIGHT + margin;
            int lineHeight = 18;

            // Calculate total content height first
            int logoHeight = 40;
            int infoLinesCount = 8; // Number of info lines (Time, FPS, Rendered Notes, etc.)
            int totalContentHeight = logoHeight + (infoLinesCount * lineHeight) + (margin * 2); // Top and bottom margin

            // Draw info panel background with calculated height
            Raylib.DrawRectangle(0, TITLE_BAR_HEIGHT, INFO_PANEL_WIDTH, totalContentHeight, INFO_PANEL_BG);

            // Kiva logo area
            Raylib.DrawText("KIVA", margin, currentY, (int)((TITLE_FONT_SIZE + 4) * FONT_SCALE), RaylibColor.WHITE);
            Raylib.DrawText("v1.1.15", margin + 90, currentY + 8, (int)(DEFAULT_FONT_SIZE * FONT_SCALE), RaylibColor.GRAY);
            currentY += 40;

            // File info - EXACT ORDER as requested, controlled by CardParams settings
            if (scene?.File != null)
            {
                var cardParams = settings.General.InfoCardParams;

                // 1. Time (always show)
                if ((cardParams & CardParams.Time) != 0)
                {
                    RenderInfoLine("Time:", FormatTime(playingState.GetTime()) + " / " + FormatTime(scene.File.MidiLength), margin, currentY);
                    currentY += lineHeight;
                }

                // 2. FPS (always show)
                if ((cardParams & CardParams.FPS) != 0)
                {
                    RenderInfoLine("FPS:", currentFPS.ToString("F1"), margin, currentY);
                    currentY += lineHeight;
                }

                // 3. Rendered Notes (always show)
                if ((cardParams & CardParams.RenderedNotes) != 0)
                {
                    RenderInfoLine("Rendered Notes:", lastRenderedNotes.ToString("#,##0"), margin, currentY);
                    currentY += lineHeight;
                }

                // 4. Note Count (always show)
                if ((cardParams & CardParams.NoteCount) != 0)
                {
                    RenderInfoLine("Note Count:", scene.File.MidiNoteCount.ToString("#,##0"), margin, currentY);
                    currentY += lineHeight;
                }

                // 5. Polyphony (always show)
                if ((cardParams & CardParams.Polyphony) != 0)
                {
                    RenderInfoLine("Polyphony:", currentPolyphony.ToString("#,##0"), margin, currentY);
                    currentY += lineHeight;
                }

                // 6. NPS (always show) - FIXED to use scene.LastNPS
                if ((cardParams & CardParams.NPS) != 0)
                {
                    RenderInfoLine("NPS:", scene.LastNPS.ToString("#,##0"), margin, currentY);
                    currentY += lineHeight;
                }

                // 7. Passed (always show) - WORKING implementation
                RenderInfoLine("Passed:", scene.NotesPassedSum.ToString("#,##0"), margin, currentY);
                currentY += lineHeight;

                // 8. Audio Buffer (always show)
                if ((cardParams & CardParams.AudioBuffer) != 0)
                {
                    if (selectedAudioEngine == AudioEngine.PreRender && preRenderPlayer != null)
                    {
                        RenderInfoLine("Audio Buffer:", FormatTime(preRenderPlayer.BufferSeconds), margin, currentY);
                    }
                    else
                    {
                        RenderInfoLine("Audio Buffer:", "0:0.0", margin, currentY);
                    }
                    currentY += lineHeight;
                }
            }
            else
            {
                // Show default values when no file loaded
                RenderInfoLine("Time:", "0:0.0 / 0:0.0", margin, currentY); currentY += lineHeight;
                RenderInfoLine("FPS:", "0.0", margin, currentY); currentY += lineHeight;
                RenderInfoLine("Rendered Notes:", "0", margin, currentY); currentY += lineHeight;
                RenderInfoLine("Note Count:", "0", margin, currentY); currentY += lineHeight;
                RenderInfoLine("Polyphony:", "0", margin, currentY); currentY += lineHeight;
                RenderInfoLine("NPS:", "0", margin, currentY); currentY += lineHeight;
                RenderInfoLine("Passed:", "0", margin, currentY); currentY += lineHeight;
                RenderInfoLine("Audio Buffer:", "0:0.0", margin, currentY); currentY += lineHeight;
            }
        }

        private void RenderInfoLine(string label, string value, int x, int y)
        {
            int fontSize = (int)(DEFAULT_FONT_SIZE * FONT_SCALE);

            // Draw label on left
            Raylib.DrawText(label, x, y, fontSize, RaylibColor.WHITE);

            // BRUTE FORCE ALIGNMENT - draw each character individually at fixed positions
            int rightEdge = INFO_PANEL_WIDTH - 15; // Fixed right edge
            int charWidth = 8; // Approximate character width for alignment

            // Reverse the string and draw each character at a fixed position from the right
            char[] chars = value.ToCharArray();
            Array.Reverse(chars);

            RaylibColor valueColor = new RaylibColor(230, 230, 230, 255);

            for (int i = 0; i < chars.Length; i++)
            {
                int charX = rightEdge - (i + 1) * charWidth; // Fixed position for each character
                Raylib.DrawText(chars[i].ToString(), charX, y, fontSize, valueColor);
            }
        }

        private void RenderStatusMessage()
        {
            if (!string.IsNullOrEmpty(statusMessage) &&
                (DateTime.UtcNow - statusMessageTime).TotalSeconds < 3)
            {
                // Use proper text measurement for accurate sizing
                int fontSize = (int)(LABEL_FONT_SIZE * FONT_SCALE);
                int textWidth = MeasureTextWidth(statusMessage, fontSize);
                int textHeight = fontSize + 4;

                // Position at bottom with proper margins
                int msgX = 15;
                int msgY = windowHeight - textHeight - 15;
                int bgWidth = textWidth + 20;
                int bgHeight = textHeight + 10;

                // Draw compact background
                Raylib.DrawRectangle(msgX - 10, msgY - 5, bgWidth, bgHeight, new RaylibColor(0, 0, 0, 180));
                Raylib.DrawRectangleLines(msgX - 10, msgY - 5, bgWidth, bgHeight, new RaylibColor(100, 150, 255, 100));

                // Draw text
                Raylib.DrawText(statusMessage, msgX, msgY, fontSize, RaylibColor.YELLOW);
            }
        }

        private void HandleButtonMouse()
        {
            if (!Raylib.IsMouseButtonPressed(MOUSE_BUTTON_LEFT))
                return;

            int mouseX = Raylib.GetMouseX();
            int mouseY = Raylib.GetMouseY();

            // Button positions (matching RenderTitleBar layout)
            int buttonY = 5;
            int buttonX = 10;

            // Open button
            if (IsMouseOverButton(mouseX, mouseY, buttonX, buttonY, BUTTON_SIZE, BUTTON_SIZE))
            {
                OpenMIDIFileDialog();
                return;
            }
            buttonX += BUTTON_SIZE + 5;

            // Pause button
            if (IsMouseOverButton(mouseX, mouseY, buttonX, buttonY, BUTTON_SIZE, BUTTON_SIZE))
            {
                if (!playingState.Paused) playingState.Pause();
                return;
            }
            buttonX += BUTTON_SIZE + 5;

            // Play button
            if (IsMouseOverButton(mouseX, mouseY, buttonX, buttonY, BUTTON_SIZE, BUTTON_SIZE))
            {
                if (scene?.File != null && playingState.Paused) playingState.Play();
                return;
            }
            buttonX += BUTTON_SIZE + 5;

            // Settings button
            if (IsMouseOverButton(mouseX, mouseY, buttonX, buttonY, BUTTON_SIZE, BUTTON_SIZE))
            {
                OpenSettingsWindow();
                return;
            }
        }

        private bool IsMouseOverButton(int mouseX, int mouseY, int buttonX, int buttonY, int buttonWidth, int buttonHeight)
        {
            return mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                   mouseY >= buttonY && mouseY <= buttonY + buttonHeight;
        }

        private void HandleSliderMouse()
        {
            int mouseX = Raylib.GetMouseX();
            int mouseY = Raylib.GetMouseY();

            // Speed slider position (matching RenderTitleBar layout)
            int buttonY = 5;
            int buttonX = 10;
            buttonX += BUTTON_SIZE + 5; // Open button
            buttonX += BUTTON_SIZE + 5; // Pause button
            buttonX += BUTTON_SIZE + 5; // Play button
            buttonX += BUTTON_SIZE + 5; // Settings button
            buttonX += 70; // "Speed:" label

            int speedSliderX = buttonX;
            int speedSliderY = buttonY + 10;
            int speedSliderWidth = SLIDER_WIDTH;
            int speedSliderHeight = 20;

            // Size slider position (updated for new layout)
            buttonX += SLIDER_WIDTH + 100; // More space to avoid overlap
            buttonX += 60; // "Size:" label

            int sizeSliderX = buttonX;
            int sizeSliderY = buttonY + 10;
            int sizeSliderWidth = SLIDER_WIDTH;
            int sizeSliderHeight = 20;

            // Handle speed slider (exactly like seek bar)
            bool mouseOverSpeedSlider = mouseX >= speedSliderX && mouseX <= speedSliderX + speedSliderWidth &&
                                       mouseY >= speedSliderY - 5 && mouseY <= speedSliderY + speedSliderHeight + 5;

            if (mouseOverSpeedSlider && Raylib.IsMouseButtonPressed(MOUSE_BUTTON_LEFT))
            {
                isDraggingSpeedSlider = true;
                speedSliderMouseX = mouseX; // Store exact mouse position
                CalculatePendingSpeedValue(mouseX, speedSliderX, speedSliderWidth);
            }

            if (isDraggingSpeedSlider)
            {
                if (Raylib.IsMouseButtonDown(MOUSE_BUTTON_LEFT))
                {
                    speedSliderMouseX = mouseX; // Update exact mouse position
                    CalculatePendingSpeedValue(mouseX, speedSliderX, speedSliderWidth);
                }
                else
                {
                    // Apply the final value when dragging stops
                    if (pendingSpeedValue >= 0)
                    {
                        playingState.ChangeSpeed(pendingSpeedValue);
                        SetStatusMessage($"Speed: {pendingSpeedValue:F2}x");
                        pendingSpeedValue = -1;
                    }
                    isDraggingSpeedSlider = false;
                    speedSliderMouseX = -1; // Reset when not dragging
                }
            }

            // Handle size slider (exactly like seek bar)
            bool mouseOverSizeSlider = mouseX >= sizeSliderX && mouseX <= sizeSliderX + sizeSliderWidth &&
                                      mouseY >= sizeSliderY - 5 && mouseY <= sizeSliderY + sizeSliderHeight + 5;

            if (mouseOverSizeSlider && Raylib.IsMouseButtonPressed(MOUSE_BUTTON_LEFT))
            {
                isDraggingSizeSlider = true;
                sizeSliderMouseX = mouseX; // Store exact mouse position
                CalculatePendingSizeValue(mouseX, sizeSliderX, sizeSliderWidth);
            }

            if (isDraggingSizeSlider)
            {
                if (Raylib.IsMouseButtonDown(MOUSE_BUTTON_LEFT))
                {
                    sizeSliderMouseX = mouseX; // Update exact mouse position
                    CalculatePendingSizeValue(mouseX, sizeSliderX, sizeSliderWidth);
                }
                else
                {
                    // Apply the final value when dragging stops
                    if (pendingSizeValue >= 0)
                    {
                        noteSpeed = pendingSizeValue;
                        if (settings.Volatile != null) settings.Volatile.Size = pendingSizeValue;
                        SetStatusMessage($"Note Size: {pendingSizeValue:F2}");
                        pendingSizeValue = -1;
                    }
                    isDraggingSizeSlider = false;
                    sizeSliderMouseX = -1; // Reset when not dragging
                }
            }

            // Handle speed number display and arrows
            int speedNumberX = speedSliderX + speedSliderWidth + 10;
            int speedArrowX = speedNumberX + 60 + 5;
            HandleNumberControls(mouseX, mouseY, speedNumberX, speedSliderY, speedArrowX, true);

            // Handle size number display and arrows
            int sizeNumberX = sizeSliderX + sizeSliderWidth + 10;
            int sizeArrowX = sizeNumberX + 60 + 5;
            HandleNumberControls(mouseX, mouseY, sizeNumberX, sizeSliderY, sizeArrowX, false);
        }

        private void HandleNumberControls(int mouseX, int mouseY, int numberX, int numberY, int arrowX, bool isSpeed)
        {
            int numberWidth = 60;
            int numberHeight = 20;
            int arrowWidth = 15;
            int arrowHeight = numberHeight / 2 - 1;

            // Check if clicking on number display (for future text editing)
            bool mouseOverNumber = mouseX >= numberX && mouseX <= numberX + numberWidth &&
                                  mouseY >= numberY && mouseY <= numberY + numberHeight;

            // Check up arrow
            bool mouseOverUpArrow = mouseX >= arrowX && mouseX <= arrowX + arrowWidth &&
                                   mouseY >= numberY && mouseY <= numberY + arrowHeight;

            // Check down arrow
            bool mouseOverDownArrow = mouseX >= arrowX && mouseX <= arrowX + arrowWidth &&
                                     mouseY >= numberY + arrowHeight + 2 && mouseY <= numberY + numberHeight;

            if (Raylib.IsMouseButtonPressed(MOUSE_BUTTON_LEFT))
            {
                if (mouseOverUpArrow)
                {
                    if (isSpeed)
                    {
                        double newSpeed = Math.Min(10.0, playingState.Speed + 0.1);
                        playingState.ChangeSpeed(newSpeed);
                        SetStatusMessage($"Speed: {newSpeed:F2}x");
                    }
                    else
                    {
                        double newSize = Math.Min(MAX_NOTE_SPEED, noteSpeed + 0.1);
                        noteSpeed = newSize;
                        if (settings.Volatile != null) settings.Volatile.Size = newSize;
                        SetStatusMessage($"Note Size: {newSize:F2}");
                    }
                }
                else if (mouseOverDownArrow)
                {
                    if (isSpeed)
                    {
                        double newSpeed = Math.Max(0.1, playingState.Speed - 0.1);
                        playingState.ChangeSpeed(newSpeed);
                        SetStatusMessage($"Speed: {newSpeed:F2}x");
                    }
                    else
                    {
                        double newSize = Math.Max(MIN_NOTE_SPEED, noteSpeed - 0.1);
                        noteSpeed = newSize;
                        if (settings.Volatile != null) settings.Volatile.Size = newSize;
                        SetStatusMessage($"Note Size: {newSize:F2}");
                    }
                }
                else if (mouseOverNumber)
                {
                    // Start text editing mode
                    if (isSpeed)
                    {
                        isEditingSpeedNumber = true;
                        isEditingSizeNumber = false;
                        speedInputText = playingState.Speed.ToString("F2");
                        SetStatusMessage("Editing speed - type new value and press Enter");
                    }
                    else
                    {
                        isEditingSizeNumber = true;
                        isEditingSpeedNumber = false;
                        sizeInputText = noteSpeed.ToString("F2");
                        SetStatusMessage("Editing size - type new value and press Enter");
                    }
                }
            }
        }

        private void HandleTextInput()
        {
            // Handle digit keys directly (simpler approach)
            int[] digitKeys = { RaylibPInvoke.KEY_0, RaylibPInvoke.KEY_1, RaylibPInvoke.KEY_2, RaylibPInvoke.KEY_3, RaylibPInvoke.KEY_4,
                               RaylibPInvoke.KEY_5, RaylibPInvoke.KEY_6, RaylibPInvoke.KEY_7, RaylibPInvoke.KEY_8, RaylibPInvoke.KEY_9 };

            for (int i = 0; i < digitKeys.Length; i++)
            {
                if (Raylib.IsKeyPressed(digitKeys[i]))
                {
                    char digit = (char)digitKeys[i];
                    if (isEditingSpeedNumber)
                    {
                        speedInputText += digit;
                    }
                    else if (isEditingSizeNumber)
                    {
                        sizeInputText += digit;
                    }
                }
            }

            // Handle decimal point (period key)
            if (Raylib.IsKeyPressed(RaylibPInvoke.KEY_PERIOD))
            {
                if (isEditingSpeedNumber && !speedInputText.Contains("."))
                {
                    speedInputText += ".";
                }
                else if (isEditingSizeNumber && !sizeInputText.Contains("."))
                {
                    sizeInputText += ".";
                }
            }

            // Handle special keys
            if (Raylib.IsKeyPressed(RaylibPInvoke.KEY_BACKSPACE))
            {
                if (isEditingSpeedNumber && speedInputText.Length > 0)
                {
                    speedInputText = speedInputText.Substring(0, speedInputText.Length - 1);
                }
                else if (isEditingSizeNumber && sizeInputText.Length > 0)
                {
                    sizeInputText = sizeInputText.Substring(0, sizeInputText.Length - 1);
                }
            }
            else if (Raylib.IsKeyPressed(RaylibPInvoke.KEY_ENTER))
            {
                // Apply the entered value
                if (isEditingSpeedNumber)
                {
                    if (double.TryParse(speedInputText, out double newSpeed))
                    {
                        newSpeed = Math.Max(0.1, Math.Min(10.0, newSpeed));
                        playingState.ChangeSpeed(newSpeed);
                        SetStatusMessage($"Speed set to: {newSpeed:F2}x");
                    }
                    else
                    {
                        SetStatusMessage("Invalid speed value");
                    }
                    isEditingSpeedNumber = false;
                    speedInputText = "";
                }
                else if (isEditingSizeNumber)
                {
                    if (double.TryParse(sizeInputText, out double newSize))
                    {
                        newSize = Math.Max(MIN_NOTE_SPEED, Math.Min(MAX_NOTE_SPEED, newSize));
                        noteSpeed = newSize;
                        if (settings.Volatile != null) settings.Volatile.Size = newSize;
                        SetStatusMessage($"Note size set to: {newSize:F2}");
                    }
                    else
                    {
                        SetStatusMessage("Invalid size value");
                    }
                    isEditingSizeNumber = false;
                    sizeInputText = "";
                }
            }
            else if (Raylib.IsKeyPressed(RaylibPInvoke.KEY_ESCAPE))
            {
                // Cancel editing
                isEditingSpeedNumber = false;
                isEditingSizeNumber = false;
                speedInputText = "";
                sizeInputText = "";
                SetStatusMessage("Text editing cancelled");
            }
        }

        private void HandleTextInputClickOff()
        {
            // Check if user clicked outside the text input areas while editing
            if ((isEditingSpeedNumber || isEditingSizeNumber) && Raylib.IsMouseButtonPressed(MOUSE_BUTTON_LEFT))
            {
                int mouseX = Raylib.GetMouseX();
                int mouseY = Raylib.GetMouseY();

                // Calculate the text input box positions (same as in RenderSliderWithControls)
                int buttonY = 5;
                int speedSliderX = 10 + BUTTON_SIZE * 4 + 15 + 70; // Position of speed slider
                int sizeSliderX = speedSliderX + SLIDER_WIDTH + 100 + 60; // Position of size slider

                // Text input box dimensions
                int numberWidth = 60;
                int numberHeight = 20;

                // Speed text box area
                int speedNumberX = speedSliderX + SLIDER_WIDTH + 10;
                int speedNumberY = buttonY + 10;

                // Size text box area
                int sizeNumberX = sizeSliderX + SLIDER_WIDTH + 10;
                int sizeNumberY = buttonY + 10;

                bool clickedInSpeedBox = (mouseX >= speedNumberX && mouseX <= speedNumberX + numberWidth &&
                                         mouseY >= speedNumberY && mouseY <= speedNumberY + numberHeight);
                bool clickedInSizeBox = (mouseX >= sizeNumberX && mouseX <= sizeNumberX + numberWidth &&
                                        mouseY >= sizeNumberY && mouseY <= sizeNumberY + numberHeight);

                // If clicked outside both text boxes, apply the current value
                if (!clickedInSpeedBox && !clickedInSizeBox)
                {
                    if (isEditingSpeedNumber)
                    {
                        if (double.TryParse(speedInputText, out double newSpeed))
                        {
                            newSpeed = Math.Max(0.1, Math.Min(10.0, newSpeed));
                            playingState.ChangeSpeed(newSpeed);
                            SetStatusMessage($"Speed set to: {newSpeed:F2}x");
                        }
                        else
                        {
                            SetStatusMessage("Invalid speed value");
                        }
                        isEditingSpeedNumber = false;
                        speedInputText = "";
                    }
                    else if (isEditingSizeNumber)
                    {
                        if (double.TryParse(sizeInputText, out double newSize))
                        {
                            newSize = Math.Max(MIN_NOTE_SPEED, Math.Min(MAX_NOTE_SPEED, newSize));
                            noteSpeed = newSize;
                            if (settings.Volatile != null) settings.Volatile.Size = newSize;
                            SetStatusMessage($"Note size set to: {newSize:F2}");
                        }
                        else
                        {
                            SetStatusMessage("Invalid size value");
                        }
                        isEditingSizeNumber = false;
                        sizeInputText = "";
                    }
                }
            }
        }

        private void CalculatePendingSpeedValue(int mouseX, int sliderX, int sliderWidth)
        {
            // Calculate position as percentage (exactly like seek bar)
            double position = (double)(mouseX - sliderX) / sliderWidth;
            position = Math.Max(0, Math.Min(1, position));

            // Convert to speed (0.1 to 10.0) but don't apply yet
            pendingSpeedValue = 0.1 + position * (10.0 - 0.1);

            // Show preview message
            SetStatusMessage($"Speed: {pendingSpeedValue:F2}x (dragging...)");
        }

        private void UpdateSpeedFromMouse(int mouseX, int sliderX, int sliderWidth)
        {
            // Calculate position as percentage (exactly like seek bar)
            double position = (double)(mouseX - sliderX) / sliderWidth;
            position = Math.Max(0, Math.Min(1, position));

            // Convert to speed (0.1 to 10.0)
            double newSpeed = 0.1 + position * (10.0 - 0.1);
            playingState.ChangeSpeed(newSpeed);

            SetStatusMessage($"Speed: {newSpeed:F2}x");
        }

        private void CalculatePendingSizeValue(int mouseX, int sliderX, int sliderWidth)
        {
            // Calculate position as percentage
            double position = (double)(mouseX - sliderX) / sliderWidth;
            position = Math.Max(0, Math.Min(1, position));

            // Convert to note speed but don't apply yet
            pendingSizeValue = MIN_NOTE_SPEED + position * (MAX_NOTE_SPEED - MIN_NOTE_SPEED);

            // Show preview message
            SetStatusMessage($"Note Size: {pendingSizeValue:F2} (dragging...)");
        }

        private void UpdateSizeFromMouse(int mouseX, int sliderX, int sliderWidth)
        {
            // Calculate position as percentage
            double position = (double)(mouseX - sliderX) / sliderWidth;
            position = Math.Max(0, Math.Min(1, position));

            // Convert to note speed (MIN_NOTE_SPEED to MAX_NOTE_SPEED)
            double newSize = MIN_NOTE_SPEED + position * (MAX_NOTE_SPEED - MIN_NOTE_SPEED);
            noteSpeed = newSize;

            // Update volatile settings
            if (settings.Volatile != null)
            {
                settings.Volatile.Size = newSize;
            }

            SetStatusMessage($"Note Size: {newSize:F2}");
        }

        private void OpenSettingsWindow()
        {
            lock (settingsWindowLock)
            {
                // If settings window is already open, bring it to front
                if (currentSettingsWindow != null && currentSettingsWindow.IsVisible)
                {
                    try
                    {
                        currentSettingsWindow.Activate();
                        currentSettingsWindow.Focus();
                        SetStatusMessage("Settings window brought to front");
                        return;
                    }
                    catch
                    {
                        // Window might be disposed, continue to create new one
                        currentSettingsWindow = null;
                    }
                }

                SetStatusMessage("Opening settings window...");

                // Create new settings window
                Task.Run(() =>
                {
                    try
                    {
                        var thread = new Thread(() =>
                        {
                            try
                            {
                                var settingsWindow = new SettingsWindow(settings);

                                // Make window topmost
                                settingsWindow.Topmost = true;

                                // Store reference
                                lock (settingsWindowLock)
                                {
                                    currentSettingsWindow = settingsWindow;
                                }

                                // Handle window closed event
                                settingsWindow.Closed += (s, e) =>
                                {
                                    lock (settingsWindowLock)
                                    {
                                        currentSettingsWindow = null;
                                    }
                                };

                                settingsWindow.ShowDialog();
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error in settings window thread: {ex.Message}");
                            }
                            finally
                            {
                                lock (settingsWindowLock)
                                {
                                    currentSettingsWindow = null;
                                }
                            }
                        });

                        thread.SetApartmentState(ApartmentState.STA);
                        thread.Start();
                        thread.Join();

                        SetStatusMessage("Settings window closed");
                    }
                    catch (Exception ex)
                    {
                        SetStatusMessage($"Error opening settings: {ex.Message}");
                        Console.WriteLine($"Error opening settings window: {ex.Message}");

                        lock (settingsWindowLock)
                        {
                            currentSettingsWindow = null;
                        }
                    }
                });
            }
        }

        private int MeasureTextWidth(string text, int fontSize)
        {
            // Approximate text width calculation for raylib default font
            return (int)(text.Length * fontSize * 0.6f);
        }

        private void UpdateFPS()
        {
            frameCount++;
            DateTime now = DateTime.UtcNow;

            // Update FPS every second
            if ((now - fpsUpdateTime).TotalSeconds >= 1.0)
            {
                currentFPS = frameCount / (now - fpsUpdateTime).TotalSeconds;
                frameCount = 0;
                fpsUpdateTime = now;
            }
        }

        private void UpdateRenderingStats()
        {
            // PERFORMANCE FIX: Update stats much less frequently to reduce CPU usage
            DateTime now = DateTime.UtcNow;
            if ((now - lastStatsUpdate).TotalMilliseconds < 250) // Update only 4 times per second instead of 60
                return;

            lastStatsUpdate = now;

            // PERFORMANCE FIX: Use scene's cached values instead of expensive calculations
            if (scene?.File != null)
            {
                // Use the renderer's already-calculated values instead of recalculating
                lastRenderedNotes = (int)scene.LastRenderedNoteCount;
                currentPolyphony = (int)scene.LastPolyphony;

                // No background tasks - use cached values for performance
            }
            else
            {
                lastRenderedNotes = 0;
                currentPolyphony = 0;
            }
        }

        // REMOVED: CountVisibleNotes() - was causing 100% CPU usage
        // Now using cached scene.LastRenderedNoteCount for performance

        // REMOVED: CalculateCurrentNPS() and CountCurrentPolyphony() - were causing 100% CPU usage
        // Now using cached scene.LastNPS and scene.LastPolyphony for performance

        private void ToggleFullscreen()
        {
            if (!isFullscreen)
            {
                // Store current windowed size before going fullscreen
                windowedWidth = windowWidth;
                windowedHeight = windowHeight;

                // Toggle to fullscreen
                Raylib.ToggleFullscreen();
                isFullscreen = true;

                // Update window size to monitor size
                windowWidth = Raylib.GetMonitorWidth(0);
                windowHeight = Raylib.GetMonitorHeight(0);

                SetStatusMessage("Fullscreen enabled (ESC to exit)");
            }
            else
            {
                // Toggle back to windowed
                Raylib.ToggleFullscreen();
                isFullscreen = false;

                // Restore windowed size
                windowWidth = windowedWidth;
                windowHeight = windowedHeight;

                SetStatusMessage("Windowed mode");
            }

            // Update scene renderer size
            scene?.SetScreenSize(windowWidth, windowHeight);
        }

        private void HandleWindowResize()
        {
            int currentWidth = Raylib.GetScreenWidth();
            int currentHeight = Raylib.GetScreenHeight();

            // Check if window size changed
            if (currentWidth != windowWidth || currentHeight != windowHeight)
            {
                windowWidth = currentWidth;
                windowHeight = currentHeight;

                // Update scene renderer size
                scene?.SetScreenSize(windowWidth, windowHeight);

                // If not fullscreen, store as windowed size
                if (!isFullscreen)
                {
                    windowedWidth = windowWidth;
                    windowedHeight = windowHeight;
                }
            }
        }

        private void SetStatusMessage(string message)
        {
            statusMessage = message;
            statusMessageTime = DateTime.UtcNow;
        }



        private string FormatTime(double seconds)
        {
            TimeSpan time = TimeSpan.FromSeconds(seconds);
            if (time.TotalHours >= 1)
            {
                return $"{(int)time.TotalHours}:{time.Minutes:D2}:{time.Seconds:D2}";
            }
            else
            {
                return $"{time.Minutes}:{time.Seconds:D2}";
            }
        }

        private void ChangeNoteSpeed(double delta)
        {
            double newSpeed = noteSpeed + delta;
            newSpeed = Math.Max(MIN_NOTE_SPEED, Math.Min(MAX_NOTE_SPEED, newSpeed));

            if (Math.Abs(newSpeed - noteSpeed) > 0.001) // Only update if actually changed
            {
                noteSpeed = newSpeed;

                // Update the settings volatile size (this controls note speed)
                if (settings?.Volatile != null)
                {
                    settings.Volatile.Size = noteSpeed;
                }

                SetStatusMessage($"Note speed: {noteSpeed:F1}x");
            }
        }

        private void SeekForward()
        {
            if (scene?.File == null)
            {
                SetStatusMessage("No MIDI file loaded");
                return;
            }

            double currentTime = playingState.GetTime();
            double newTime = currentTime + SEEK_STEP;
            double maxTime = scene.File.MidiLength;

            newTime = Math.Min(newTime, maxTime);

            playingState.Navigate(newTime);
            SetStatusMessage($"Seek: {newTime:F1}s / {maxTime:F1}s");
        }

        private void SeekBackward()
        {
            if (scene?.File == null)
            {
                SetStatusMessage("No MIDI file loaded");
                return;
            }

            double currentTime = playingState.GetTime();
            double newTime = currentTime - SEEK_STEP;

            newTime = Math.Max(newTime, 0.0);

            playingState.Navigate(newTime);
            SetStatusMessage($"Seek: {newTime:F1}s / {scene.File.MidiLength:F1}s");
        }

        private void HandleSeekBarMouse()
        {
            if (scene?.File == null)
                return;

            // Time slider is now in the title bar (moved down slightly)
            int timeSliderY = TITLE_BAR_HEIGHT - 20;
            int timeSliderMargin = 10;
            int timeSliderWidth = windowWidth - (timeSliderMargin * 2);
            int timeSliderHeight = 15;

            int mouseX = Raylib.GetMouseX();
            int mouseY = Raylib.GetMouseY();

            // Check if mouse is over time slider area
            bool mouseOverSeekBar = mouseX >= timeSliderMargin && mouseX <= timeSliderMargin + timeSliderWidth &&
                                   mouseY >= timeSliderY - 5 && mouseY <= timeSliderY + timeSliderHeight + 5;

            if (mouseOverSeekBar)
            {
                if (Raylib.IsMouseButtonPressed(MOUSE_BUTTON_LEFT))
                {
                    isDraggingSeekBar = true;
                    SeekToMousePosition(mouseX, timeSliderMargin, timeSliderWidth);
                }
            }

            if (isDraggingSeekBar)
            {
                if (Raylib.IsMouseButtonDown(MOUSE_BUTTON_LEFT))
                {
                    SeekToMousePosition(mouseX, timeSliderMargin, timeSliderWidth);
                }
                else
                {
                    isDraggingSeekBar = false;
                }
            }
        }

        private void SeekToMousePosition(int mouseX, int margin, int seekBarWidth)
        {
            if (scene?.File == null)
                return;

            // Calculate position as percentage
            double position = (double)(mouseX - margin) / seekBarWidth;
            position = Math.Max(0, Math.Min(1, position));

            // Convert to time
            double newTime = position * scene.File.MidiLength;

            playingState.Navigate(newTime);
            SetStatusMessage($"Seek: {newTime:F1}s / {scene.File.MidiLength:F1}s");
        }

        private void ToggleSegmentRendering()
        {
            // Segment rendering is always enabled - this is just for user feedback
            SetStatusMessage("Segment-based rendering is always enabled");
        }

        public void Dispose()
        {
            isRunning = false;

            // PROPERLY DISPOSE all resources to prevent memory leaks
            try
            {
                // Stop playback first
                StopPlayback();

                // Dispose audio engines
                midiPlayer?.Dispose();
                preRenderPlayer?.Dispose();
                midiPlayer = null;
                preRenderPlayer = null;

                // Clear MIDI file reference
                if (scene?.File != null)
                {
                    scene.File = null;
                }

                // Dispose scene
                scene?.Dispose();
                scene = null;

                // Force garbage collection
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                Console.WriteLine("All resources properly disposed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during disposal: {ex.Message}");
            }
        }
    }


}
