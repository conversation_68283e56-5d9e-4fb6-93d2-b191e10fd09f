// TextureLoader.cs - Optimized Version
using Raylib_cs;
using System.Collections.Concurrent;
using System.Numerics;

namespace SharpMIDI.Renderer
{
    public static class TextureLoader
    {
        public struct TextureSegment
        {
            public RenderTexture2D texture;
            public float startTime;
            public float endTime;
            public float pixelsPerMs;
            public int width;
            public bool isLoaded;
            public bool isLoading;
            public long lastAccessTime;
            public int noteCount; // Cache note count for priority
        }

        private const int MAX_TEXTURE_WIDTH = 16384;
        private const int MAX_LOADED_SEGMENTS = 3;
        private const float SEGMENT_PRELOAD_TIME = 1000f;
        private const int PAD = 55;
        private const int BATCH_SIZE = 1000; // Process notes in batches
        private const int MAX_LOADING_THREADS = 2;

        private static TextureSegment[] textureSegments = Array.Empty<TextureSegment>();
        private static Dictionary<int, TextureCompiler.EnhancedNote[]> segmentNoteCache = new();
        private static readonly Queue<RenderTexture2D> reusableTextures = new();
        private static long accessCounter = 0;

        // Async loading system
        public static readonly ConcurrentQueue<int> loadingQueue = new();
        private static readonly SemaphoreSlim loadingSemaphore = new(MAX_LOADING_THREADS);
        private static readonly CancellationTokenSource cancellationTokenSource = new();
        private static Task[]? loadingTasks;

        // Pre-computed rendering data for hot segments
        private static readonly ConcurrentDictionary<int, PrecomputedRenderData> precomputedData = new();

        private struct PrecomputedRenderData
        {
            public Raylib_cs.Rectangle[] rectangles;
            public Raylib_cs.Color[] colors;
            public int count;
        }

        public static int LoadedSegmentCount => textureSegments.Count(s => s.isLoaded);
        public static int TotalSegmentCount => textureSegments.Length;
        public static int LoadingSegmentCount => textureSegments.Count(s => s.isLoading);

        static TextureLoader()
        {
            // Start background loading tasks
            loadingTasks = new Task[MAX_LOADING_THREADS];
            for (int i = 0; i < MAX_LOADING_THREADS; i++)
            {
                int taskId = i;
                loadingTasks[i] = Task.Run(() => BackgroundLoader(taskId, cancellationTokenSource.Token));
            }
        }

        public static float CreateTextureSegments(TextureCompiler.EnhancedNote[] allNotes, float totalDuration, float ppm)
        {
            float segmentDuration = MAX_TEXTURE_WIDTH / ppm;
            int count = (int)Math.Ceiling(totalDuration / segmentDuration);
            if (count == 1)
            {
                segmentDuration = totalDuration;
                ppm = Math.Min(ppm, MAX_TEXTURE_WIDTH / totalDuration);
            }

            textureSegments = Enumerable.Range(0, count).Select(i =>
            {
                float start = i * segmentDuration;
                float end = Math.Min(totalDuration, (i + 1) * segmentDuration);
                int width = (int)Math.Ceiling((end - start) * ppm);
                
                // Count notes in this segment for priority sorting
                int noteCount = allNotes.Count(n => n.endTime > start && n.startTime < end);
                
                return new TextureSegment
                {
                    startTime = start,
                    endTime = end,
                    pixelsPerMs = ppm,
                    width = width,
                    noteCount = noteCount
                };
            }).ToArray();
            return segmentDuration;
        }

        public static void PrepareSegmentNoteCache(TextureCompiler.EnhancedNote[] allNotes)
        {
            segmentNoteCache.Clear();
            precomputedData.Clear();
            
            Parallel.For(0, textureSegments.Length, i =>
            {
                var seg = textureSegments[i];
                var notes = allNotes.Where(n => n.endTime > seg.startTime && n.startTime < seg.endTime).ToArray();
                
                lock (segmentNoteCache) 
                    segmentNoteCache[i] = notes;

                // Precompute render data for segments with many notes
                if (notes.Length > 5000)
                {
                    PrecomputeRenderData(i, notes, seg);
                }
            });
        }

        private static void PrecomputeRenderData(int segmentIndex, TextureCompiler.EnhancedNote[] notes, TextureSegment segment)
        {
            const int topPad = PAD / 3;
            const int bottomPad = PAD - 4 - topPad;
            float yscale = (Raylib.GetScreenHeight() - topPad - bottomPad) / 128f;

            var rectangles = new Raylib_cs.Rectangle[notes.Length];
            var colors = new Raylib_cs.Color[notes.Length];

            for (int i = 0; i < notes.Length; i++)
            {
                var note = notes[i];
                float start = Math.Max(note.startTime, segment.startTime);
                float end = Math.Min(note.endTime, segment.endTime);
                float x1 = (start - segment.startTime) * segment.pixelsPerMs;
                float x2 = (end - segment.startTime) * segment.pixelsPerMs;
                float width = MathF.Max(1f, x2 - x1);
                float y = Raylib.GetScreenHeight() - bottomPad - note.noteNumber * yscale;

                rectangles[i] = new Raylib_cs.Rectangle(x1, y, width, note.height);
                colors[i] = new Raylib_cs.Color
                {
                    R = (byte)(note.color >> 16),
                    G = (byte)(note.color >> 8),
                    B = (byte)(note.color),
                    A = 255
                };
            }

            precomputedData[segmentIndex] = new PrecomputedRenderData
            {
                rectangles = rectangles,
                colors = colors,
                count = notes.Length
            };
        }

        public static void ManageTextureMemory(float currentTick)
        {
            if (textureSegments.Length == 0) return;

            float window = GetWindow();
            float halfWindow = window * 0.5f;
            float viewportStart = currentTick - halfWindow;
            float viewportEnd = currentTick + halfWindow + SEGMENT_PRELOAD_TIME;

            var neededSegments = new List<(int index, float priority)>();

            for (int i = 0; i < textureSegments.Length; i++)
            {
                var s = textureSegments[i];
                if (s.endTime > viewportStart && s.startTime < viewportEnd)
                {
                    // Priority based on distance from current tick and note density
                    float distance = Math.Abs(s.startTime + (s.endTime - s.startTime) * 0.5f - currentTick);
                    float priority = 1f / (distance + 100f) * (1f + s.noteCount * 0.0001f);
                    neededSegments.Add((i, priority));
                }
            }

            // Sort by priority (higher first)
            neededSegments.Sort((a, b) => b.priority.CompareTo(a.priority));

            long now = ++accessCounter;
            var loaded = new List<(int index, long lastAccess)>();

            for (int i = 0; i < textureSegments.Length; i++)
            {
                var s = textureSegments[i];
                if (!s.isLoaded) continue;

                bool isNeeded = neededSegments.Any(ns => ns.index == i);
                if (isNeeded)
                {
                    textureSegments[i].lastAccessTime = now;
                    loaded.Add((i, now));
                }
                else
                {
                    loaded.Add((i, s.lastAccessTime));
                }
            }

            if (loaded.Count > MAX_LOADED_SEGMENTS)
            {
                loaded.Sort((a, b) => a.lastAccess.CompareTo(b.lastAccess));
                int toUnload = loaded.Count - MAX_LOADED_SEGMENTS;

                for (int i = 0; i < toUnload; i++)
                {
                    int index = loaded[i].index;
                    if (!neededSegments.Any(ns => ns.index == index))
                        UnloadSegment(index);
                }
            }

            // Queue segments for loading in priority order
            foreach (var (index, priority) in neededSegments.Take(MAX_LOADED_SEGMENTS))
            {
                if (!textureSegments[index].isLoaded && !textureSegments[index].isLoading)
                {
                    textureSegments[index].isLoading = true;
                    loadingQueue.Enqueue(index);
                }
            }
        }

        private static async Task BackgroundLoader(int taskId, CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                if (loadingQueue.TryDequeue(out int segmentIndex))
                {
                    await loadingSemaphore.WaitAsync(cancellationToken);
                    try
                    {
                        // Load segment on background thread (prepare data)
                        await Task.Run(() => LoadSegmentAsync(segmentIndex), cancellationToken);
                    }
                    finally
                    {
                        loadingSemaphore.Release();
                    }
                }
                else
                {
                    await Task.Delay(16, cancellationToken); // ~60fps check rate
                }
            }
        }

        private static void LoadSegmentAsync(int index)
        {
            if (index < 0 || index >= textureSegments.Length) return;
            var segment = textureSegments[index];
            if (segment.isLoaded) 
            {
                textureSegments[index].isLoading = false;
                return;
            }

            // This must run on main thread - queue for main thread execution
            lock (mainThreadQueue)
            {
                mainThreadQueue.Enqueue(() => LoadSegmentOnMainThread(index));
            }
        }

        private static readonly Queue<Action> mainThreadQueue = new();

        public static void ProcessMainThreadQueue()
        {
            // Process a limited number of texture operations per frame to avoid hitches
            int maxOperations = 2;
            int processed = 0;

            lock (mainThreadQueue)
            {
                while (mainThreadQueue.Count > 0 && processed < maxOperations)
                {
                    var action = mainThreadQueue.Dequeue();
                    action();
                    processed++;
                }
            }
        }

        private static void LoadSegmentOnMainThread(int index)
        {
            if (index < 0 || index >= textureSegments.Length) return;
            var segment = textureSegments[index];
            if (segment.isLoaded) 
            {
                textureSegments[index].isLoading = false;
                return;
            }

            segment.texture = GetReusableTexture(segment.width);
            Raylib.BeginTextureMode(segment.texture);
            Raylib.ClearBackground(Raylib_cs.Color.Black);

            // Use precomputed data if available for heavy segments
            if (precomputedData.TryGetValue(index, out var renderData))
            {
                // Batch render using precomputed rectangles
                RenderNotesInBatches(renderData.rectangles, renderData.colors, renderData.count);
            }
            else
            {
                // Fallback to original rendering for lighter segments
                RenderSegmentTraditional(index, segment);
            }

            Raylib.EndTextureMode();
            segment.isLoaded = true;
            segment.isLoading = false;
            segment.lastAccessTime = ++accessCounter;
            textureSegments[index] = segment;
        }

        private static void RenderNotesInBatches(Raylib_cs.Rectangle[] rectangles, Raylib_cs.Color[] colors, int count)
        {
            // Process in batches to avoid frame drops
            for (int i = 0; i < count; i += BATCH_SIZE)
            {
                int batchEnd = Math.Min(i + BATCH_SIZE, count);
                for (int j = i; j < batchEnd; j++)
                {
                    var rect = rectangles[j];
                    Raylib.DrawRectangle((int)rect.X, (int)rect.Y, (int)rect.Width, (int)rect.Height, colors[j]);
                }
            }
        }

        private static void RenderSegmentTraditional(int index, TextureSegment segment)
        {
            const int topPad = PAD / 3;
            const int bottomPad = PAD - 4 - topPad;
            float yscale = (Raylib.GetScreenHeight() - topPad - bottomPad) / 128f;

            if (!segmentNoteCache.TryGetValue(index, out var notes))
            {
                notes = TextureCompiler.AllNotes
                    .Where(n => n.endTime > segment.startTime && n.startTime < segment.endTime)
                    .ToArray();

                lock (segmentNoteCache)
                    segmentNoteCache[index] = notes;
            }

            foreach (var note in notes)
            {
                float start = Math.Max(note.startTime, segment.startTime);
                float end = Math.Min(note.endTime, segment.endTime);
                float x1 = (start - segment.startTime) * segment.pixelsPerMs;
                float x2 = (end - segment.startTime) * segment.pixelsPerMs;
                float width = MathF.Max(1f, x2 - x1);
                float y = Raylib.GetScreenHeight() - bottomPad - note.noteNumber * yscale;

                Raylib_cs.Color c = new()
                {
                    R = (byte)(note.color >> 16),
                    G = (byte)(note.color >> 8),
                    B = (byte)(note.color),
                    A = 255
                };

                Raylib.DrawRectangle((int)x1, (int)y, (int)width, note.height, c);
            }
        }

        private static void UnloadSegment(int index)
        {
            if (index < 0 || index >= textureSegments.Length) return;
            ref var segment = ref textureSegments[index];
            if (!segment.isLoaded) return;

            if (segment.texture.Id != 0)
            {
                lock (reusableTextures)
                    reusableTextures.Enqueue(segment.texture);
            }

            segment.texture = default;
            segment.isLoaded = false;
            segment.isLoading = false;
            segment.lastAccessTime = 0;
        }

        public static void DrawScrollingTextures(float tick, int W, int H, float window)
        {
            // Process main thread queue for texture loading
            ProcessMainThreadQueue();

            if (!TextureRenderer.ready || textureSegments.Length == 0) return;

            float halfWindow = window * 0.5f;
            float viewStart = tick - halfWindow;
            float viewEnd = tick + halfWindow;

            for (int i = 0; i < textureSegments.Length; i++)
            {
                var seg = textureSegments[i];
                if (!seg.isLoaded || seg.endTime <= viewStart || seg.startTime >= viewEnd)
                    continue;

                float visibleStart = Math.Max(viewStart, seg.startTime);
                float visibleEnd = Math.Min(viewEnd, seg.endTime);

                float srcX = (visibleStart - seg.startTime) * seg.pixelsPerMs;
                float srcW = (visibleEnd - visibleStart) * seg.pixelsPerMs;

                if (srcW <= 0) continue;

                float dstX = (visibleStart - viewStart) / window * W;
                float dstW = (visibleEnd - visibleStart) / window * W;

                Raylib_cs.Rectangle src = new()
                {
                    X = srcX,
                    Y = 0,
                    Width = srcW,
                    Height = -H // flip
                };

                Raylib_cs.Rectangle dst = new()
                {
                    X = dstX,
                    Y = 0,
                    Width = dstW,
                    Height = H
                };

                Raylib.DrawTexturePro(seg.texture.Texture, src, dst, Vector2.Zero, 0f, Raylib_cs.Color.White);
            }
        }

        public static void DrawGlowEffect(float tick, int W, int H, float window)
        {
            if (!TextureRenderer.ready || TextureCompiler.AllNotes.Length == 0) return;

            float viewStart = tick - window * 0.5f;
            float scale = W / window;

            const int topPad = PAD / 3;
            const int bottomPad = PAD - 4 - topPad;
            float yscale = (H - topPad - bottomPad) / 128f;

            foreach (var note in TextureCompiler.AllNotes)
            {
                if (tick < note.startTime || tick > note.endTime) continue;

                float x1 = (note.startTime - viewStart) * scale;
                float x2 = (note.endTime - viewStart) * scale;

                if (x2 <= 0 || x1 >= W) continue;

                x1 = MathF.Max(0, x1);
                x2 = MathF.Min(W, x2);
                if (x2 <= x1) continue;

                uint c = note.color;
                Raylib_cs.Color glow = new()
                {
                    R = (byte)Math.Min(255, ((c >> 14) & 0x3FC)),
                    G = (byte)Math.Min(255, ((c >> 6) & 0x3FC)),
                    B = (byte)Math.Min(255, ((c << 2) & 0x3FC)),
                    A = 180
                };

                float y = H - bottomPad - note.noteNumber * yscale;
                Raylib.DrawRectangle((int)x1, (int)y - 1, (int)(x2 - x1), note.height + 2, glow);
            }
        }

        public static void CleanupTextures()
        {
            cancellationTokenSource.Cancel();
            
            // Wait for loading tasks to complete
            if (loadingTasks != null)
            {
                Task.WaitAll(loadingTasks, TimeSpan.FromSeconds(1));
            }

            foreach (var segment in textureSegments)
            {
                if (segment.texture.Id != 0)
                    Raylib.UnloadRenderTexture(segment.texture);
            }

            lock (reusableTextures)
            {
                while (reusableTextures.Count > 0)
                {
                    var tex = reusableTextures.Dequeue();
                    if (tex.Id != 0) Raylib.UnloadRenderTexture(tex);
                }
            }

            textureSegments = Array.Empty<TextureSegment>();
            segmentNoteCache.Clear();
            precomputedData.Clear();
            TextureRenderer.ready = false;
        }

        private static RenderTexture2D GetReusableTexture(int width)
        {
            lock (reusableTextures)
            {
                while (reusableTextures.Count > 0)
                {
                    var tex = reusableTextures.Dequeue();
                    if (tex.Texture.Width == width) return tex;
                    Raylib.UnloadRenderTexture(tex);
                }
            }
            return Raylib.LoadRenderTexture(width, Raylib.GetScreenHeight());
        }

        private static float GetWindow() => typeof(TextureRenderer).GetField("window", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static) is { } f ? (float)f.GetValue(null)! : 2000f;
    }
}