// TextureCompiler.cs
using System.Collections.Concurrent;
using SharpMIDI;

namespace SharpMIDI.Renderer
{
    public static class TextureCompiler
    {

        public struct EnhancedNote
        {
            public float startTime, endTime;
            public int noteNumber;
            public byte velocity, height;
            public uint color;
            public int trackIndex;
        }

        private const uint BK = 0x54A;
        private const float OPTIMAL_PIXELS_PER_MS = 2.0f;

        private static readonly byte[] noteHeights = new byte[128]; 
        private static readonly uint[] trackColors = new uint[256];
        private static readonly ConcurrentBag<List<EnhancedNote>> noteListPool = new();
        private static readonly ConcurrentBag<Dictionary<int, (float, byte)>> activeDictPool = new();

        public static EnhancedNote[] AllNotes { get; private set; } = Array.Empty<EnhancedNote>();
        public static float TotalDuration { get; private set; }
        public static float SegmentDuration { get; private set; }

        static TextureCompiler()
        {
            for (int i = 0; i < 128; i++)
                noteHeights[i] = (byte)(((BK >> (i % 12)) & 1) != 0 ? 6 : 12);

            const float G = 1.618034f;
            for (int i = 0; i < 256; i++)
            {
                float h = (i * G * 360f) % 360f, c = 0.72f;
                float x = c * (1f - MathF.Abs((h / 60f) % 2f - 1f));
                int s = (int)(h / 60f) % 6;
                float r = 0, g = 0, b = 0;
                switch (s)
                {
                    case 0: r = c; g = x; break;
                    case 1: r = x; g = c; break;
                    case 2: g = c; b = x; break;
                    case 3: g = x; b = c; break;
                    case 4: r = x; b = c; break;
                    default: r = c; b = x; break;
                }
                r += 0.18f; g += 0.18f; b += 0.18f;
                trackColors[i] = 0xFF000000 | ((uint)(r * 255) << 16) | ((uint)(g * 255) << 8) | (uint)(b * 255);
            }

            int poolSize = Math.Max(4, Environment.ProcessorCount * 2);
            for (int i = 0; i < poolSize; i++)
            {
                noteListPool.Add(new List<EnhancedNote>());
                activeDictPool.Add(new Dictionary<int, (float, byte)>());
            }
        }

        public static EnhancedNote[] ProcessTrack(int trackIndex, dynamic track)
        {
            var noteList = noteListPool.TryTake(out var nl) ? nl : new();
            var activeNotes = activeDictPool.TryTake(out var ad) ? ad : new();
            noteList.Clear(); activeNotes.Clear();

            float time = 0f;
            foreach (var ev in track.synthEvents)
            {
                time += ev.pos;
                int status = ev.val & 0xF0, ch = ev.val & 0x0F;
                int note = (ev.val >> 8) & 0x7F, vel = (ev.val >> 16) & 0x7F;
                if (note > 127) continue;
                int key = (ch << 7) | note;
                if (status == 0x90 && vel > 0) activeNotes[key] = (time, (byte)vel);
                else if (status == 0x80 || (status == 0x90 && vel == 0))
                {
                    if (activeNotes.Remove(key, out var info))
                        noteList.Add(new EnhancedNote
                        {
                            startTime = info.Item1,
                            endTime = time,
                            noteNumber = note,
                            velocity = info.Item2,
                            height = noteHeights[note],
                            color = trackColors[trackIndex & 255],
                            trackIndex = trackIndex
                        });
                }
            }

            foreach (var kvp in activeNotes)
            {
                int note = kvp.Key & 127;
                noteList.Add(new EnhancedNote
                {
                    startTime = kvp.Value.Item1,
                    endTime = time + 100f,
                    noteNumber = note,
                    velocity = kvp.Value.Item2,
                    height = noteHeights[note],
                    color = trackColors[trackIndex & 255],
                    trackIndex = trackIndex
                });
            }

            var result = noteList.ToArray();
            noteListPool.Add(noteList);
            activeDictPool.Add(activeNotes);
            return result;
        }

        public static void EnhanceTracksForRendering()
        {
            TextureRenderer.ready = false;
            TextureLoader.CleanupTextures();

            var validTracks = MIDIPlayer.tracks
                ?.Select((track, index) => (track, index))
                .Where(t => t.track?.synthEvents.Count > 0)
                .ToList();

            if (validTracks == null || validTracks.Count == 0)
            {
                AllNotes = Array.Empty<EnhancedNote>();
                TextureRenderer.ready = true;
                return;
            }

            var trackResults = new ConcurrentBag<EnhancedNote[]>();
            Parallel.ForEach(validTracks, new ParallelOptions { MaxDegreeOfParallelism = Environment.ProcessorCount },
                t =>
                {
                    var notes = ProcessTrack(t.index, t.track);
                    if (notes.Length > 0) trackResults.Add(notes);
                });

            AllNotes = trackResults.SelectMany(x => x).OrderBy(n => n.startTime).ThenBy(n => n.trackIndex).ToArray();
            TotalDuration = AllNotes.Max(n => n.endTime);

            float segmentDuration;
            segmentDuration = TextureLoader.CreateTextureSegments(AllNotes, TotalDuration, OPTIMAL_PIXELS_PER_MS);
            TextureLoader.PrepareSegmentNoteCache(AllNotes);

            TextureRenderer.ready = true;
        }
    }
}
